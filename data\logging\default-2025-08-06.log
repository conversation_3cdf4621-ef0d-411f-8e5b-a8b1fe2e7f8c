{"level":30,"time":1754501187145,"pid":22260,"module":"server","module":"key-provider","service":"openai","keyCount":3,"msg":"Loaded OpenAI keys."}
{"level":40,"time":1754501187146,"pid":22260,"module":"server","module":"key-provider","service":"anthropic","msg":"ANTHROPIC_KEYS is not set. Anthropic API will not be available."}
{"level":40,"time":1754501187146,"pid":22260,"module":"server","module":"key-provider","service":"google-ai","msg":"GOOGLE_AI_KEYS is not set. Google AI API will not be available."}
{"level":40,"time":1754501187146,"pid":22260,"module":"server","module":"key-provider","service":"deepseek","msg":"DEEPSEEK_KEYS is not set. DeepSeek API will not be available."}
{"level":40,"time":1754501187147,"pid":22260,"module":"server","module":"key-provider","service":"xai","msg":"XAI_KEYS is not set. XAI API will not be available."}
{"level":40,"time":1754501187147,"pid":22260,"module":"server","module":"key-provider","service":"groq","msg":"GROQ_KEYS is not set. Groq API will not be available."}
{"level":30,"time":1754501187155,"pid":22260,"module":"server","module":"cidr","list":"ADMIN_WHITELIST","ranges":{"ADMIN_WHITELIST":[[{"octets":[0,0,0,0]},0],[{"parts":[0,0,0,0,0,0,0,0]},0]]},"msg":"IP whitelist configured"}
{"level":20,"time":1754501187156,"pid":22260,"module":"server","module":"check-risu-token","msg":"Importing Risu public key"}
{"level":20,"time":1754501187159,"pid":22260,"module":"server","module":"check-risu-token","msg":"Imported Risu public key"}
{"level":30,"time":1754501187181,"pid":22260,"module":"server","module":"cidr","list":"IP_BLACKLIST","ranges":{"IP_BLACKLIST":[]},"msg":"IP blacklist configured"}
{"level":30,"time":1754501187181,"pid":22260,"module":"server","msg":"Server starting up..."}
{"level":30,"time":1754501187303,"pid":22260,"module":"server","build":"0a0c675 (modified) (main@yae-miko/ai-reverse-proxy)","status":"M src/admin/web/views/admin_index.ejs\n M src/proxy/middleware/response/index.ts\n M src/proxy/middleware/response/log-event.ts","changes":true,"msg":"Got build info from Git."}
{"level":30,"time":1754501187304,"pid":22260,"module":"server","msg":"Checking configs and external dependencies..."}
{"level":30,"time":1754501187305,"pid":22260,"module":"server","module":"key-checker","service":"openai","msg":"Starting key checker..."}
{"level":30,"time":1754501187305,"pid":22260,"module":"server","module":"key-checker","service":"anthropic","msg":"Starting key checker..."}
{"level":30,"time":1754501187305,"pid":22260,"module":"server","module":"key-checker","service":"google-ai","msg":"Starting key checker..."}
{"level":30,"time":1754501187305,"pid":22260,"module":"server","module":"key-checker","service":"deepseek","msg":"Starting key checker..."}
{"level":30,"time":1754501187305,"pid":22260,"module":"server","module":"key-checker","service":"xai","msg":"Starting key checker..."}
{"level":30,"time":1754501187306,"pid":22260,"module":"server","module":"key-checker","service":"groq","msg":"Starting key checker..."}
{"level":30,"time":1754501187320,"pid":22260,"module":"server","rule":"0 2,10,18 * * *","next":"2025-08-07T02:00:00.000+07:00","msg":"Scheduled periodic key recheck job"}
{"level":30,"time":1754501187771,"pid":22260,"module":"server","module":"database","msg":"Initializing database SQLite..."}
{"level":20,"time":1754501187784,"pid":22260,"module":"server","module":"database","msg":"Query: \n\t\t\tCREATE TABLE IF NOT EXISTS \"__drizzle_migrations\" (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t"}
{"level":20,"time":1754501187784,"pid":22260,"module":"server","module":"database","msg":"Query: SELECT id, hash, created_at FROM \"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1"}
{"level":20,"time":1754501187784,"pid":22260,"module":"server","module":"database","msg":"Query: BEGIN"}
{"level":20,"time":1754501187784,"pid":22260,"module":"server","module":"database","msg":"Query: COMMIT"}
{"level":30,"time":1754501187784,"pid":22260,"module":"server","module":"database","msg":"Database SQLite initialized."}
{"level":30,"time":1754501187784,"pid":22260,"module":"server","msg":"Starting request queue..."}
{"level":10,"time":1754501187785,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754501187785,"pid":22260,"module":"server","module":"request-queue","msg":"Started request queue."}
{"level":30,"time":1754501187794,"pid":22260,"module":"server","build":"0a0c675 (modified) (main@yae-miko/ai-reverse-proxy)","msg":"Startup complete."}
{"level":20,"time":1754501187796,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"nqhej2","timeoutId":7,"numEnabled":3,"numUnchecked":3,"msg":"Scheduling next check..."}
{"level":30,"time":1754501187796,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"nqhej2","timeoutId":7,"batch":["oai-2b5179a0","oai-3b36688b","oai-ce384488"],"remaining":0,"newTimeoutId":17,"msg":"Scheduled batch of initial checks."}
{"level":40,"time":1754501187796,"pid":22260,"module":"server","module":"key-checker","service":"anthropic","callId":"0pnhhw","timeoutId":8,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754501187797,"pid":22260,"module":"server","module":"key-checker","service":"google-ai","callId":"z72dy4","timeoutId":9,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754501187798,"pid":22260,"module":"server","module":"key-checker","service":"deepseek","callId":"au8nju","timeoutId":10,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754501187799,"pid":22260,"module":"server","module":"key-checker","service":"xai","callId":"njzc9a","timeoutId":11,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754501187805,"pid":22260,"module":"server","module":"key-checker","service":"groq","callId":"j0cgdc","timeoutId":12,"msg":"All keys are disabled. Stopping."}
{"level":30,"time":1754501187819,"pid":22260,"module":"server","port":7860,"interface":"0.0.0.0","msg":"Server ready to accept connections."}
{"level":20,"time":1754501188051,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","msg":"Checking key..."}
{"level":20,"time":1754501188064,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","msg":"Checking key..."}
{"level":20,"time":*************,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","msg":"Checking key..."}
{"level":40,"time":*************,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","error":{"error":{"message":"Incorrect API key provided: sk-proj-********************************************wX1c. You can find your API key at https://platform.openai.com/account/api-keys.","type":"invalid_request_error","param":null,"code":"invalid_api_key"}},"msg":"Key is invalid or revoked. Disabling key."}
{"level":40,"time":*************,"pid":22260,"module":"server","module":"key-provider","service":"openai","key":"oai-ce384488","reason":"revoked","disabledBy":"key-checker","msg":"Key disabled"}
{"level":30,"time":*************,"pid":22260,"module":"server","module":"key-checker","service":"openai","parent":"oai-2b5179a0","organizations":["org-4zsLVMQwd6XiqMlUZrXT3mda","org-Lk9jdNUbwX1N42B0bYwi2EPF"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":*************,"pid":22260,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-d797d9b5","parentHash":"oai-2b5179a0","orgId":"org-Lk9jdNUbwX1N42B0bYwi2EPF","msg":"Cloned organization key"}
{"level":30,"time":*************,"pid":22260,"module":"server","module":"key-checker","service":"openai","parent":"oai-3b36688b","organizations":["org-fQ5wuMw3OxhV7ey3NZWgVHkr","org-upWt1FsbEIzoFcryj0sGHAPy"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":1754501189371,"pid":22260,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-8280c3e9","parentHash":"oai-3b36688b","orgId":"org-fQ5wuMw3OxhV7ey3NZWgVHkr","msg":"Cloned organization key"}
{"level":30,"time":1754501189709,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002","ft:gpt-3.5-turbo-0613:techtouch::7qabuPyG"],"msg":"Checked key."}
{"level":30,"time":1754501190473,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002"],"msg":"Checked key."}
{"level":30,"time":1754501190473,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"nqhej2","timeoutId":7,"msg":"Batch complete."}
{"level":20,"time":1754501190474,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"vhbate","timeoutId":17,"numEnabled":4,"numUnchecked":2,"msg":"Scheduling next check..."}
{"level":30,"time":1754501190474,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"vhbate","timeoutId":17,"batch":["oai-d797d9b5","oai-8280c3e9"],"remaining":0,"newTimeoutId":60,"msg":"Scheduled batch of initial checks."}
{"level":20,"time":1754501190737,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-d797d9b5","msg":"Checking key..."}
{"level":20,"time":1754501190738,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-8280c3e9","msg":"Checking key..."}
{"level":40,"time":1754501191245,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-d797d9b5","rateLimitType":"insufficient_quota","error":{"error":{"message":"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.","type":"insufficient_quota","param":null,"code":"insufficient_quota"}},"msg":"Key returned a non-transient 429 error. Disabling key."}
{"level":40,"time":1754501191246,"pid":22260,"module":"server","module":"key-provider","service":"openai","key":"oai-d797d9b5","reason":"quota","disabledBy":"key-checker","msg":"Key disabled"}
{"level":40,"time":1754501194359,"pid":22260,"module":"server","module":"key-checker","service":"openai","key":"oai-8280c3e9","rateLimitType":"insufficient_quota","error":{"error":{"message":"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.","type":"insufficient_quota","param":null,"code":"insufficient_quota"}},"msg":"Key returned a non-transient 429 error. Disabling key."}
{"level":40,"time":1754501194360,"pid":22260,"module":"server","module":"key-provider","service":"openai","key":"oai-8280c3e9","reason":"quota","disabledBy":"key-checker","msg":"Key disabled"}
{"level":30,"time":1754501194360,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"vhbate","timeoutId":17,"msg":"Batch complete."}
{"level":20,"time":1754501194360,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"txzyr4","timeoutId":60,"numEnabled":2,"numUnchecked":0,"msg":"Scheduling next check..."}
{"level":30,"time":1754501194360,"pid":22260,"module":"server","module":"key-checker","service":"openai","callId":"txzyr4","timeoutId":60,"msg":"Initial checks complete and recurring checks are disabled for this service. Stopping."}
{"level":30,"time":1754501199068,"pid":22260,"module":"server","reqId":1,"req":{"id":1,"method":"GET","url":"/","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51527},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"6118","etag":"W/\"17e6-2sfYK9GiWL+95s1KyLYYWZ4Z/nU\""}},"responseTime":57,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 57ms"}
{"level":30,"time":1754501199143,"pid":22260,"module":"server","reqId":2,"req":{"id":2,"method":"GET","url":"/res/css/reset.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2"},"remoteAddress":"127.0.0.1","remotePort":51527},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 01 May 2025 14:15:58 GMT","content-type":"text/css; charset=UTF-8","content-length":"6601"}},"responseTime":10,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/reset.css - 10ms"}
{"level":30,"time":1754501199148,"pid":22260,"module":"server","reqId":3,"req":{"id":3,"method":"GET","url":"/res/css/sakura.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2"},"remoteAddress":"127.0.0.1","remotePort":51536},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 01 May 2025 14:15:58 GMT","content-type":"text/css; charset=UTF-8","content-length":"4424"}},"responseTime":12,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/sakura.css - 12ms"}
{"level":30,"time":1754501199149,"pid":22260,"module":"server","reqId":4,"req":{"id":4,"method":"GET","url":"/res/css/sakura-dark.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2"},"remoteAddress":"127.0.0.1","remotePort":51537},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 01 May 2025 14:15:58 GMT","content-type":"text/css; charset=UTF-8","content-length":"4156"}},"responseTime":12,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/sakura-dark.css - 12ms"}
{"level":30,"time":1754501199253,"pid":22260,"module":"server","reqId":5,"req":{"id":5,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6"},"remoteAddress":"127.0.0.1","remotePort":51527},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":2,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 2ms"}
{"level":10,"time":1754501207789,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501227805,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501247135,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501247135,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501247805,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501267827,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501287849,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501307146,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501307147,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501307849,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501327861,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501347856,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501367136,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501367136,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501367852,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501387865,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501407869,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501427142,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501427142,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501427870,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501447868,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754501449553,"pid":22260,"module":"server","reqId":6,"req":{"id":6,"method":"GET","url":"/admin/","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":302,"headers":{"access-control-allow-origin":"*","location":"/admin/login","vary":"Accept","content-type":"text/html; charset=utf-8","content-length":"41","set-cookie":"********"}},"responseTime":10,"msg":"Request complete - GET \u001b[32m302\u001b[39m / - 10ms"}
{"level":30,"time":1754501449587,"pid":22260,"module":"server","reqId":7,"req":{"id":7,"method":"GET","url":"/admin/login","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"5462","etag":"W/\"1556-pAc9ieUXpC5q1l1jHoh4SXE34oI\""}},"responseTime":15,"msg":"Request complete - GET \u001b[32m200\u001b[39m /login - 15ms"}
{"level":30,"time":1754501449654,"pid":22260,"module":"server","reqId":8,"req":{"id":8,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/login","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Mon, 28 Jul 2025 19:52:43 GMT","content-type":"text/css; charset=UTF-8","content-length":"34800"}},"responseTime":3,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 3ms"}
{"level":30,"time":1754501459858,"pid":22260,"module":"server","reqId":9,"req":{"id":9,"method":"POST","url":"/admin/login","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","content-type":"application/x-www-form-urlencoded","content-length":"218","origin":"http://localhost:7860","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=0"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","date":"Wed, 06 Aug 2025 17:30:59 GMT","content-type":"application/json; charset=utf-8","content-length":"46","etag":"W/\"2e-sbmaP1h2c7UAB4xf01/jiXyQKIA\"","set-cookie":"********","x-ratelimit-limit":"10","x-ratelimit-remaining":"9","x-ratelimit-reset":"1754502360"}},"responseTime":19,"msg":"Request complete - POST \u001b[32m200\u001b[39m /login - 19ms"}
{"level":30,"time":1754501459904,"pid":22260,"module":"server","reqId":10,"req":{"id":10,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/login","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52968","etag":"W/\"cee8-tGwjeNMoy7Vji1q1Znpd19oevPg\""}},"responseTime":12,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 12ms"}
{"level":20,"time":1754501460206,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501460240,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501460269,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501460392,"pid":22260,"module":"server","reqId":11,"req":{"id":11,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"2910184","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":189,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 189ms"}
{"level":20,"time":1754501460400,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501460418,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501460490,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753896660]"}
{"level":20,"time":1754501460491,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501460580,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415060]"}
{"level":20,"time":1754501460580,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909460]"}
{"level":20,"time":1754501460581,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501460581,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501460601,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501460677,"pid":22260,"module":"server","reqId":12,"req":{"id":12,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51550},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1763655","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":283,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chart-data?timeRange=all - 283ms"}
{"level":30,"time":1754501460680,"pid":22260,"module":"server","reqId":13,"req":{"id":13,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51551},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"337","etag":"W/\"151-celN5mwyGSmO2Crp/6NzrNiDhGA\""}},"responseTime":286,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 286ms"}
{"level":10,"time":1754501467884,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501473694,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1749317473]"}
{"level":20,"time":1754501473753,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501473846,"pid":22260,"module":"server","reqId":14,"req":{"id":14,"method":"GET","url":"/admin/api/events/chart-data?timeRange=60d","query":{"timeRange":"60d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1763655","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":163,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chart-data?timeRange=60d - 163ms"}
{"level":20,"time":1754501475516,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1751909475]"}
{"level":20,"time":1754501475539,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501475590,"pid":22260,"module":"server","reqId":15,"req":{"id":15,"method":"GET","url":"/admin/api/events/chart-data?timeRange=30d","query":{"timeRange":"30d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"760150","etag":"W/\"b9956-q7/XbLlCNYTVKY3FQhtIm29ZHb8\""}},"responseTime":78,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chart-data?timeRange=30d - 78ms"}
{"level":20,"time":1754501476521,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501476574,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501476709,"pid":22260,"module":"server","reqId":16,"req":{"id":16,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51543},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":193,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 193ms"}
{"level":20,"time":1754501487143,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501487143,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501487889,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501507892,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501527897,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501547133,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501547133,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501547897,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501567890,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501587911,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501607146,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501607146,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501607914,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501627917,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501647929,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501667152,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501667152,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501667930,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501687957,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501707950,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501727178,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501727178,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501727973,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501747986,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501767980,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501769321,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1746725769]"}
{"level":20,"time":1754501769383,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501769518,"pid":22260,"module":"server","reqId":17,"req":{"id":17,"method":"GET","url":"/admin/api/events/chart-data?timeRange=90d","query":{"timeRange":"90d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51605},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1763655","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":204,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chart-data?timeRange=90d - 204ms"}
{"level":20,"time":1754501772904,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1749317772]"}
{"level":20,"time":1754501772963,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501773068,"pid":22260,"module":"server","reqId":18,"req":{"id":18,"method":"GET","url":"/admin/api/events/chart-data?timeRange=60d","query":{"timeRange":"60d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51605},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":168,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=60d - 168ms"}
{"level":20,"time":1754501774514,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501774574,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501774663,"pid":22260,"module":"server","reqId":19,"req":{"id":19,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51605},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":152,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 152ms"}
{"level":20,"time":1754501775723,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1746725775]"}
{"level":20,"time":1754501775767,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501775872,"pid":22260,"module":"server","reqId":20,"req":{"id":20,"method":"GET","url":"/admin/api/events/chart-data?timeRange=90d","query":{"timeRange":"90d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51605},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":156,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=90d - 156ms"}
{"level":30,"time":1754501776397,"pid":22260,"module":"server","reqId":21,"req":{"id":21,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51622},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52968","etag":"W/\"cee8-tGwjeNMoy7Vji1q1Znpd19oevPg\""}},"responseTime":6,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 6ms"}
{"level":30,"time":1754501776468,"pid":22260,"module":"server","reqId":22,"req":{"id":22,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51622},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Mon, 28 Jul 2025 19:52:43 GMT","content-type":"text/css; charset=UTF-8","content-length":"34800"}},"responseTime":2,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 2ms"}
{"level":20,"time":1754501776881,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501776894,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501776896,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501776976,"pid":22260,"module":"server","reqId":23,"req":{"id":23,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51622},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"2910184","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":96,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 96ms"}
{"level":20,"time":1754501776981,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501776998,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501777048,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753896977]"}
{"level":20,"time":1754501777049,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501777133,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415377]"}
{"level":20,"time":1754501777133,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909777]"}
{"level":20,"time":1754501777133,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501777134,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501777158,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501777260,"pid":22260,"module":"server","reqId":24,"req":{"id":24,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51625},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1763655","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":283,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chart-data?timeRange=all - 283ms"}
{"level":30,"time":1754501777263,"pid":22260,"module":"server","reqId":25,"req":{"id":25,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51626},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"337","etag":"W/\"151-Ogv1sk5bQ9AWcMoRqmY/TA0d5wM\""}},"responseTime":285,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 285ms"}
{"level":30,"time":1754501777414,"pid":22260,"module":"server","reqId":26,"req":{"id":26,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51626},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":2,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 2ms"}
{"level":20,"time":1754501780533,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1749317780]"}
{"level":20,"time":1754501780598,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501780698,"pid":22260,"module":"server","reqId":27,"req":{"id":27,"method":"GET","url":"/admin/api/events/chart-data?timeRange=60d","query":{"timeRange":"60d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51622},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":169,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=60d - 169ms"}
{"level":20,"time":1754501787176,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501787176,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501787982,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501807995,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501828003,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501846022,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501846074,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501846189,"pid":22260,"module":"server","reqId":28,"req":{"id":28,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51622},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":176,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 176ms"}
{"level":30,"time":1754501847092,"pid":22260,"module":"server","reqId":29,"req":{"id":29,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52968","etag":"W/\"cee8-qYvbmyLoH37+QT8m1sMbr0V1z+c\""}},"responseTime":11,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 11ms"}
{"level":20,"time":1754501847178,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501847181,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":30,"time":1754501847188,"pid":22260,"module":"server","reqId":30,"req":{"id":30,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Mon, 28 Jul 2025 19:52:43 GMT","content-type":"text/css; charset=UTF-8","content-length":"34800"}},"responseTime":12,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 12ms"}
{"level":30,"time":1754501847749,"pid":22260,"module":"server","reqId":31,"req":{"id":31,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":1,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 1ms"}
{"level":20,"time":1754501847755,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501847772,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501847775,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501847854,"pid":22260,"module":"server","reqId":32,"req":{"id":32,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"2910184","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":100,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 100ms"}
{"level":20,"time":1754501847861,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501847874,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501848037,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753897048]"}
{"level":20,"time":1754501848037,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501848123,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415448]"}
{"level":20,"time":1754501848124,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909848]"}
{"level":20,"time":1754501848124,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501848124,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501848144,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":10,"time":1754501848205,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754501848237,"pid":22260,"module":"server","reqId":33,"req":{"id":33,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51669},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1763655","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":382,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chart-data?timeRange=all - 382ms"}
{"level":30,"time":1754501848245,"pid":22260,"module":"server","reqId":34,"req":{"id":34,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":51670},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"337","etag":"W/\"151-QUw/CpMY2hT71TyQzpXPNYuNjXw\""}},"responseTime":389,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 389ms"}
{"level":10,"time":1754501868226,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754501873762,"pid":22260,"module":"server","reqId":35,"req":{"id":35,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"cee8-qYvbmyLoH37+QT8m1sMbr0V1z+c\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52962","etag":"W/\"cee2-3gAafy0qybuoSiJn4w/vzWk7il0\""}},"responseTime":9,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 9ms"}
{"level":20,"time":1754501874010,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501874022,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501874026,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501874121,"pid":22260,"module":"server","reqId":36,"req":{"id":36,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":120,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 120ms"}
{"level":20,"time":1754501874125,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501874142,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501874204,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753897074]"}
{"level":20,"time":1754501874205,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501874293,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415474]"}
{"level":20,"time":1754501874294,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909874]"}
{"level":20,"time":1754501874294,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501874296,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501874318,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501874407,"pid":22260,"module":"server","reqId":37,"req":{"id":37,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51669},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":288,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 288ms"}
{"level":30,"time":1754501874410,"pid":22260,"module":"server","reqId":38,"req":{"id":38,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"151-QUw/CpMY2hT71TyQzpXPNYuNjXw\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51670},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"338","etag":"W/\"152-0yre5flFfjOAiG4MGSqbTT1RAVo\""}},"responseTime":290,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 290ms"}
{"level":30,"time":1754501882245,"pid":22260,"module":"server","reqId":39,"req":{"id":39,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"cee2-3gAafy0qybuoSiJn4w/vzWk7il0\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52965","etag":"W/\"cee5-wIi54XxvlLtBVoglJ7VRKahKuZo\""}},"responseTime":22,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 22ms"}
{"level":20,"time":1754501882526,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501882544,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501882550,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501882676,"pid":22260,"module":"server","reqId":40,"req":{"id":40,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":150,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 150ms"}
{"level":20,"time":1754501882678,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501882702,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501882770,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753897082]"}
{"level":20,"time":1754501882771,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501882871,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415482]"}
{"level":20,"time":1754501882872,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909882]"}
{"level":20,"time":1754501882873,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501882874,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501882893,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501882977,"pid":22260,"module":"server","reqId":41,"req":{"id":41,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51669},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":303,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 303ms"}
{"level":30,"time":1754501882983,"pid":22260,"module":"server","reqId":42,"req":{"id":42,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"152-0yre5flFfjOAiG4MGSqbTT1RAVo\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51670},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"338","etag":"W/\"152-0zrHnNBT6wgOZ6G7feYa8b61eTw\""}},"responseTime":308,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 308ms"}
{"level":10,"time":1754501888239,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754501893656,"pid":22260,"module":"server","reqId":43,"req":{"id":43,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"cee5-wIi54XxvlLtBVoglJ7VRKahKuZo\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52970","etag":"W/\"ceea-a0pPzMA7qeMMP6+DfI+mHS80YJ0\""}},"responseTime":31,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 31ms"}
{"level":20,"time":1754501893960,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501893974,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501893977,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501894060,"pid":22260,"module":"server","reqId":44,"req":{"id":44,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":102,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 102ms"}
{"level":20,"time":1754501894062,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501894079,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501894136,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753897094]"}
{"level":20,"time":1754501894136,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501894216,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415494]"}
{"level":20,"time":1754501894217,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909894]"}
{"level":20,"time":1754501894218,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501894218,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501894230,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501894308,"pid":22260,"module":"server","reqId":45,"req":{"id":45,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51669},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":250,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 250ms"}
{"level":30,"time":1754501894314,"pid":22260,"module":"server","reqId":46,"req":{"id":46,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"152-0zrHnNBT6wgOZ6G7feYa8b61eTw\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51670},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"338","etag":"W/\"152-sIlV1quqUfbQpVQq6UfkwnYjwhM\""}},"responseTime":253,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 253ms"}
{"level":20,"time":1754501898913,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1749317898]"}
{"level":20,"time":1754501898962,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501899054,"pid":22260,"module":"server","reqId":47,"req":{"id":47,"method":"GET","url":"/admin/api/events/chart-data?timeRange=60d","query":{"timeRange":"60d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":145,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=60d - 145ms"}
{"level":20,"time":1754501907166,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501907166,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754501908229,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754501928247,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754501939760,"pid":22260,"module":"server","reqId":48,"req":{"id":48,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"ceea-a0pPzMA7qeMMP6+DfI+mHS80YJ0\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52968","etag":"W/\"cee8-qYvbmyLoH37+QT8m1sMbr0V1z+c\""}},"responseTime":6,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 6ms"}
{"level":20,"time":1754501939992,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501940004,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501940006,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501940084,"pid":22260,"module":"server","reqId":49,"req":{"id":49,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":94,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 94ms"}
{"level":20,"time":1754501940087,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501940105,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501940154,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753897140]"}
{"level":20,"time":1754501940155,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501940234,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415540]"}
{"level":20,"time":1754501940235,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909940]"}
{"level":20,"time":1754501940236,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501940236,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501940252,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501940355,"pid":22260,"module":"server","reqId":50,"req":{"id":50,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51669},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":273,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 273ms"}
{"level":30,"time":1754501940359,"pid":22260,"module":"server","reqId":51,"req":{"id":51,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"152-sIlV1quqUfbQpVQq6UfkwnYjwhM\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51670},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"338","etag":"W/\"152-qWWZ+wyVtlSeqektAyJQZaWpR5M\""}},"responseTime":276,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 276ms"}
{"level":10,"time":1754501948262,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501967179,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754501967179,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":30,"time":1754501967904,"pid":22260,"module":"server","reqId":52,"req":{"id":52,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","if-none-match":"W/\"cee8-qYvbmyLoH37+QT8m1sMbr0V1z+c\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52965","etag":"W/\"cee5-DIHM+qKwfXmUNBf69hPiOoPotJk\""}},"responseTime":18,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 18ms"}
{"level":10,"time":1754501968268,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754501968350,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501968367,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501968371,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501968459,"pid":22260,"module":"server","reqId":53,"req":{"id":53,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":111,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 111ms"}
{"level":20,"time":1754501968468,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501968484,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501968538,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753897168]"}
{"level":20,"time":1754501968539,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501968642,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415568]"}
{"level":20,"time":1754501968643,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909968]"}
{"level":20,"time":1754501968644,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501968645,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501968664,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501968769,"pid":22260,"module":"server","reqId":54,"req":{"id":54,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51669},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":312,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 312ms"}
{"level":30,"time":1754501968774,"pid":22260,"module":"server","reqId":55,"req":{"id":55,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"152-qWWZ+wyVtlSeqektAyJQZaWpR5M\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51670},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"337","etag":"W/\"151-jr9w9NWBvQSmfP+YeIRwBV8REAU\""}},"responseTime":315,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 315ms"}
{"level":20,"time":1754501973003,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1751909973]"}
{"level":20,"time":1754501973038,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501973153,"pid":22260,"module":"server","reqId":56,"req":{"id":56,"method":"GET","url":"/admin/api/events/chart-data?timeRange=30d","query":{"timeRange":"30d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"b9956-q7/XbLlCNYTVKY3FQhtIm29ZHb8\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"b9956-q7/XbLlCNYTVKY3FQhtIm29ZHb8\""}},"responseTime":157,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=30d - 157ms"}
{"level":10,"time":1754501988266,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754501989502,"pid":22260,"module":"server","reqId":57,"req":{"id":57,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"cee5-DIHM+qKwfXmUNBf69hPiOoPotJk\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"52965","etag":"W/\"cee5-umTuaJ4r4Yf4+FGfo5v1Xn6wo2o\""}},"responseTime":13,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 13ms"}
{"level":20,"time":1754501989806,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501989822,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1754501989825,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1754501989935,"pid":22260,"module":"server","reqId":58,"req":{"id":58,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"2c67e8-CCGCjpQUuQ6I3SgxRI3yS1Kj5cA\""}},"responseTime":130,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 130ms"}
{"level":20,"time":1754501989954,"pid":22260,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1754501989970,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501990031,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753897190]"}
{"level":20,"time":1754501990032,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501990126,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1754415590]"}
{"level":20,"time":1754501990129,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751909990]"}
{"level":20,"time":1754501990129,"pid":22260,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1754501990129,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1754501990153,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501990237,"pid":22260,"module":"server","reqId":59,"req":{"id":59,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51669},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":313,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=all - 313ms"}
{"level":30,"time":1754501990240,"pid":22260,"module":"server","reqId":60,"req":{"id":60,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"151-jr9w9NWBvQSmfP+YeIRwBV8REAU\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51670},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"338","etag":"W/\"152-LS6xYSKUCF48KCTATX/wt7TztKc\""}},"responseTime":311,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 311ms"}
{"level":20,"time":1754501992933,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where (\"users_events\".\"type\" = ? and createdAt >= ?) order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\", 1746725992]"}
{"level":20,"time":1754501993001,"pid":22260,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":30,"time":1754501993149,"pid":22260,"module":"server","reqId":61,"req":{"id":61,"method":"GET","url":"/admin/api/events/chart-data?timeRange=90d","query":{"timeRange":"90d"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":51666},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"1ae947-G3gLpFDvwhYssOwaFh9Vyxpeui4\""}},"responseTime":225,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chart-data?timeRange=90d - 225ms"}
{"level":10,"time":1754502008272,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754502027175,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754502027175,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754502028282,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754502048285,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754502068281,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754502087177,"pid":22260,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754502087177,"pid":22260,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754502088296,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754502108310,"pid":22260,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754502127638,"pid":13152,"module":"server","module":"key-provider","service":"openai","keyCount":3,"msg":"Loaded OpenAI keys."}
{"level":40,"time":1754502127639,"pid":13152,"module":"server","module":"key-provider","service":"anthropic","msg":"ANTHROPIC_KEYS is not set. Anthropic API will not be available."}
{"level":40,"time":1754502127640,"pid":13152,"module":"server","module":"key-provider","service":"google-ai","msg":"GOOGLE_AI_KEYS is not set. Google AI API will not be available."}
{"level":40,"time":1754502127640,"pid":13152,"module":"server","module":"key-provider","service":"deepseek","msg":"DEEPSEEK_KEYS is not set. DeepSeek API will not be available."}
{"level":40,"time":1754502127640,"pid":13152,"module":"server","module":"key-provider","service":"xai","msg":"XAI_KEYS is not set. XAI API will not be available."}
{"level":40,"time":1754502127640,"pid":13152,"module":"server","module":"key-provider","service":"groq","msg":"GROQ_KEYS is not set. Groq API will not be available."}
{"level":30,"time":1754502127653,"pid":13152,"module":"server","module":"cidr","list":"ADMIN_WHITELIST","ranges":{"ADMIN_WHITELIST":[[{"octets":[0,0,0,0]},0],[{"parts":[0,0,0,0,0,0,0,0]},0]]},"msg":"IP whitelist configured"}
{"level":20,"time":1754502127657,"pid":13152,"module":"server","module":"check-risu-token","msg":"Importing Risu public key"}
{"level":20,"time":1754502127657,"pid":13152,"module":"server","module":"check-risu-token","msg":"Imported Risu public key"}
{"level":30,"time":1754502127693,"pid":13152,"module":"server","module":"cidr","list":"IP_BLACKLIST","ranges":{"IP_BLACKLIST":[]},"msg":"IP blacklist configured"}
{"level":30,"time":1754502127694,"pid":13152,"module":"server","msg":"Server starting up..."}
{"level":30,"time":1754502128214,"pid":13152,"module":"server","build":"c86db69 (modified) (main@yae-miko/ai-reverse-proxy)","status":"M src/shared/models.ts","changes":true,"msg":"Got build info from Git."}
{"level":30,"time":1754502128216,"pid":13152,"module":"server","msg":"Checking configs and external dependencies..."}
{"level":30,"time":1754502128222,"pid":13152,"module":"server","module":"key-checker","service":"openai","msg":"Starting key checker..."}
{"level":30,"time":1754502128223,"pid":13152,"module":"server","module":"key-checker","service":"anthropic","msg":"Starting key checker..."}
{"level":30,"time":1754502128224,"pid":13152,"module":"server","module":"key-checker","service":"google-ai","msg":"Starting key checker..."}
{"level":30,"time":1754502128226,"pid":13152,"module":"server","module":"key-checker","service":"deepseek","msg":"Starting key checker..."}
{"level":30,"time":1754502128227,"pid":13152,"module":"server","module":"key-checker","service":"xai","msg":"Starting key checker..."}
{"level":30,"time":1754502128228,"pid":13152,"module":"server","module":"key-checker","service":"groq","msg":"Starting key checker..."}
{"level":30,"time":1754502128254,"pid":13152,"module":"server","rule":"0 2,10,18 * * *","next":"2025-08-07T02:00:00.000+07:00","msg":"Scheduled periodic key recheck job"}
{"level":30,"time":1754502128966,"pid":13152,"module":"server","module":"database","msg":"Initializing database SQLite..."}
{"level":20,"time":1754502128973,"pid":13152,"module":"server","module":"database","msg":"Query: \n\t\t\tCREATE TABLE IF NOT EXISTS \"__drizzle_migrations\" (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t"}
{"level":20,"time":1754502128973,"pid":13152,"module":"server","module":"database","msg":"Query: SELECT id, hash, created_at FROM \"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1"}
{"level":20,"time":1754502128973,"pid":13152,"module":"server","module":"database","msg":"Query: BEGIN"}
{"level":20,"time":1754502128973,"pid":13152,"module":"server","module":"database","msg":"Query: COMMIT"}
{"level":30,"time":1754502128973,"pid":13152,"module":"server","module":"database","msg":"Database SQLite initialized."}
{"level":30,"time":1754502128974,"pid":13152,"module":"server","msg":"Starting request queue..."}
{"level":10,"time":1754502128975,"pid":13152,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754502128975,"pid":13152,"module":"server","module":"request-queue","msg":"Started request queue."}
{"level":30,"time":1754502128989,"pid":13152,"module":"server","build":"c86db69 (modified) (main@yae-miko/ai-reverse-proxy)","msg":"Startup complete."}
{"level":20,"time":1754502128991,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"07xds0","timeoutId":7,"numEnabled":3,"numUnchecked":3,"msg":"Scheduling next check..."}
{"level":30,"time":1754502128991,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"07xds0","timeoutId":7,"batch":["oai-2b5179a0","oai-3b36688b","oai-ce384488"],"remaining":0,"newTimeoutId":17,"msg":"Scheduled batch of initial checks."}
{"level":40,"time":1754502128992,"pid":13152,"module":"server","module":"key-checker","service":"anthropic","callId":"trukft","timeoutId":8,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502128993,"pid":13152,"module":"server","module":"key-checker","service":"google-ai","callId":"ma9cyw","timeoutId":9,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502128993,"pid":13152,"module":"server","module":"key-checker","service":"deepseek","callId":"hipdjx","timeoutId":10,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502128994,"pid":13152,"module":"server","module":"key-checker","service":"xai","callId":"34t8q3","timeoutId":11,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502128994,"pid":13152,"module":"server","module":"key-checker","service":"groq","callId":"6dfelv","timeoutId":12,"msg":"All keys are disabled. Stopping."}
{"level":30,"time":1754502129009,"pid":13152,"module":"server","port":7860,"interface":"0.0.0.0","msg":"Server ready to accept connections."}
{"level":20,"time":1754502129357,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","msg":"Checking key..."}
{"level":20,"time":1754502129420,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","msg":"Checking key..."}
{"level":20,"time":*************,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","msg":"Checking key..."}
{"level":40,"time":*************,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","error":{"error":{"message":"Incorrect API key provided: sk-proj-********************************************wX1c. You can find your API key at https://platform.openai.com/account/api-keys.","type":"invalid_request_error","param":null,"code":"invalid_api_key"}},"msg":"Key is invalid or revoked. Disabling key."}
{"level":40,"time":*************,"pid":13152,"module":"server","module":"key-provider","service":"openai","key":"oai-ce384488","reason":"revoked","disabledBy":"key-checker","msg":"Key disabled"}
{"level":30,"time":*************,"pid":13152,"module":"server","module":"key-checker","service":"openai","parent":"oai-3b36688b","organizations":["org-fQ5wuMw3OxhV7ey3NZWgVHkr","org-upWt1FsbEIzoFcryj0sGHAPy"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":*************,"pid":13152,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-8280c3e9","parentHash":"oai-3b36688b","orgId":"org-fQ5wuMw3OxhV7ey3NZWgVHkr","msg":"Cloned organization key"}
{"level":30,"time":*************,"pid":13152,"module":"server","module":"key-checker","service":"openai","parent":"oai-2b5179a0","organizations":["org-4zsLVMQwd6XiqMlUZrXT3mda","org-Lk9jdNUbwX1N42B0bYwi2EPF"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":*************,"pid":13152,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-d797d9b5","parentHash":"oai-2b5179a0","orgId":"org-Lk9jdNUbwX1N42B0bYwi2EPF","msg":"Cloned organization key"}
{"level":30,"time":1754502131054,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002"],"msg":"Checked key."}
{"level":30,"time":1754502132878,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002","ft:gpt-3.5-turbo-0613:techtouch::7qabuPyG"],"msg":"Checked key."}
{"level":30,"time":1754502132879,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"07xds0","timeoutId":7,"msg":"Batch complete."}
{"level":20,"time":1754502132887,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"gdf6xw","timeoutId":17,"numEnabled":4,"numUnchecked":2,"msg":"Scheduling next check..."}
{"level":30,"time":1754502132887,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"gdf6xw","timeoutId":17,"batch":["oai-8280c3e9","oai-d797d9b5"],"remaining":0,"newTimeoutId":77,"msg":"Scheduled batch of initial checks."}
{"level":20,"time":1754502133140,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-8280c3e9","msg":"Checking key..."}
{"level":20,"time":1754502133142,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-d797d9b5","msg":"Checking key..."}
{"level":40,"time":1754502133539,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-8280c3e9","rateLimitType":"insufficient_quota","error":{"error":{"message":"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.","type":"insufficient_quota","param":null,"code":"insufficient_quota"}},"msg":"Key returned a non-transient 429 error. Disabling key."}
{"level":40,"time":1754502133540,"pid":13152,"module":"server","module":"key-provider","service":"openai","key":"oai-8280c3e9","reason":"quota","disabledBy":"key-checker","msg":"Key disabled"}
{"level":40,"time":1754502133853,"pid":13152,"module":"server","module":"key-checker","service":"openai","key":"oai-d797d9b5","rateLimitType":"insufficient_quota","error":{"error":{"message":"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.","type":"insufficient_quota","param":null,"code":"insufficient_quota"}},"msg":"Key returned a non-transient 429 error. Disabling key."}
{"level":40,"time":1754502133853,"pid":13152,"module":"server","module":"key-provider","service":"openai","key":"oai-d797d9b5","reason":"quota","disabledBy":"key-checker","msg":"Key disabled"}
{"level":30,"time":1754502133853,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"gdf6xw","timeoutId":17,"msg":"Batch complete."}
{"level":20,"time":1754502133853,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"elby8y","timeoutId":77,"numEnabled":2,"numUnchecked":0,"msg":"Scheduling next check..."}
{"level":30,"time":1754502133853,"pid":13152,"module":"server","module":"key-checker","service":"openai","callId":"elby8y","timeoutId":77,"msg":"Initial checks complete and recurring checks are disabled for this service. Stopping."}
{"level":10,"time":1754502148988,"pid":13152,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754502169002,"pid":13152,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754502187646,"pid":13152,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754502187646,"pid":13152,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754502189013,"pid":13152,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754502209219,"pid":16072,"module":"server","module":"key-provider","service":"openai","keyCount":3,"msg":"Loaded OpenAI keys."}
{"level":40,"time":1754502209219,"pid":16072,"module":"server","module":"key-provider","service":"anthropic","msg":"ANTHROPIC_KEYS is not set. Anthropic API will not be available."}
{"level":40,"time":1754502209220,"pid":16072,"module":"server","module":"key-provider","service":"google-ai","msg":"GOOGLE_AI_KEYS is not set. Google AI API will not be available."}
{"level":40,"time":1754502209220,"pid":16072,"module":"server","module":"key-provider","service":"deepseek","msg":"DEEPSEEK_KEYS is not set. DeepSeek API will not be available."}
{"level":40,"time":1754502209221,"pid":16072,"module":"server","module":"key-provider","service":"xai","msg":"XAI_KEYS is not set. XAI API will not be available."}
{"level":40,"time":1754502209221,"pid":16072,"module":"server","module":"key-provider","service":"groq","msg":"GROQ_KEYS is not set. Groq API will not be available."}
{"level":30,"time":1754502209236,"pid":16072,"module":"server","module":"cidr","list":"ADMIN_WHITELIST","ranges":{"ADMIN_WHITELIST":[[{"octets":[0,0,0,0]},0],[{"parts":[0,0,0,0,0,0,0,0]},0]]},"msg":"IP whitelist configured"}
{"level":20,"time":1754502209238,"pid":16072,"module":"server","module":"check-risu-token","msg":"Importing Risu public key"}
{"level":20,"time":1754502209238,"pid":16072,"module":"server","module":"check-risu-token","msg":"Imported Risu public key"}
{"level":30,"time":1754502209271,"pid":16072,"module":"server","module":"cidr","list":"IP_BLACKLIST","ranges":{"IP_BLACKLIST":[]},"msg":"IP blacklist configured"}
{"level":30,"time":1754502209272,"pid":16072,"module":"server","msg":"Server starting up..."}
{"level":30,"time":1754502209524,"pid":16072,"module":"server","build":"c86db69 (modified) (main@yae-miko/ai-reverse-proxy)","status":"M src/shared/models.ts","changes":true,"msg":"Got build info from Git."}
{"level":30,"time":1754502209524,"pid":16072,"module":"server","msg":"Checking configs and external dependencies..."}
{"level":30,"time":1754502209526,"pid":16072,"module":"server","module":"key-checker","service":"openai","msg":"Starting key checker..."}
{"level":30,"time":1754502209527,"pid":16072,"module":"server","module":"key-checker","service":"anthropic","msg":"Starting key checker..."}
{"level":30,"time":1754502209531,"pid":16072,"module":"server","module":"key-checker","service":"google-ai","msg":"Starting key checker..."}
{"level":30,"time":1754502209532,"pid":16072,"module":"server","module":"key-checker","service":"deepseek","msg":"Starting key checker..."}
{"level":30,"time":1754502209532,"pid":16072,"module":"server","module":"key-checker","service":"xai","msg":"Starting key checker..."}
{"level":30,"time":1754502209533,"pid":16072,"module":"server","module":"key-checker","service":"groq","msg":"Starting key checker..."}
{"level":30,"time":1754502209543,"pid":16072,"module":"server","rule":"0 2,10,18 * * *","next":"2025-08-07T02:00:00.000+07:00","msg":"Scheduled periodic key recheck job"}
{"level":30,"time":1754502210332,"pid":16072,"module":"server","module":"database","msg":"Initializing database SQLite..."}
{"level":20,"time":1754502210337,"pid":16072,"module":"server","module":"database","msg":"Query: \n\t\t\tCREATE TABLE IF NOT EXISTS \"__drizzle_migrations\" (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t"}
{"level":20,"time":1754502210337,"pid":16072,"module":"server","module":"database","msg":"Query: SELECT id, hash, created_at FROM \"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1"}
{"level":20,"time":1754502210337,"pid":16072,"module":"server","module":"database","msg":"Query: BEGIN"}
{"level":20,"time":1754502210337,"pid":16072,"module":"server","module":"database","msg":"Query: COMMIT"}
{"level":30,"time":1754502210337,"pid":16072,"module":"server","module":"database","msg":"Database SQLite initialized."}
{"level":30,"time":1754502210337,"pid":16072,"module":"server","msg":"Starting request queue..."}
{"level":20,"time":1754502210338,"pid":16072,"module":"server","module":"key-checker","service":"openai","callId":"yvqh0k","timeoutId":7,"numEnabled":3,"numUnchecked":3,"msg":"Scheduling next check..."}
{"level":30,"time":1754502210338,"pid":16072,"module":"server","module":"key-checker","service":"openai","callId":"yvqh0k","timeoutId":7,"batch":["oai-2b5179a0","oai-3b36688b","oai-ce384488"],"remaining":0,"newTimeoutId":14,"msg":"Scheduled batch of initial checks."}
{"level":40,"time":1754502210338,"pid":16072,"module":"server","module":"key-checker","service":"anthropic","callId":"gmlh9u","timeoutId":8,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502210339,"pid":16072,"module":"server","module":"key-checker","service":"google-ai","callId":"65duu9","timeoutId":9,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502210339,"pid":16072,"module":"server","module":"key-checker","service":"deepseek","callId":"yoei1t","timeoutId":10,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502210339,"pid":16072,"module":"server","module":"key-checker","service":"xai","callId":"tfaqkf","timeoutId":11,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502210340,"pid":16072,"module":"server","module":"key-checker","service":"groq","callId":"tpcob8","timeoutId":12,"msg":"All keys are disabled. Stopping."}
{"level":20,"time":1754502210615,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","msg":"Checking key..."}
{"level":20,"time":1754502210633,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","msg":"Checking key..."}
{"level":20,"time":*************,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","msg":"Checking key..."}
{"level":40,"time":*************,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","error":{"error":{"message":"Incorrect API key provided: sk-proj-********************************************wX1c. You can find your API key at https://platform.openai.com/account/api-keys.","type":"invalid_request_error","param":null,"code":"invalid_api_key"}},"msg":"Key is invalid or revoked. Disabling key."}
{"level":40,"time":*************,"pid":16072,"module":"server","module":"key-provider","service":"openai","key":"oai-ce384488","reason":"revoked","disabledBy":"key-checker","msg":"Key disabled"}
{"level":30,"time":*************,"pid":16072,"module":"server","module":"key-checker","service":"openai","parent":"oai-2b5179a0","organizations":["org-4zsLVMQwd6XiqMlUZrXT3mda","org-Lk9jdNUbwX1N42B0bYwi2EPF"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":*************,"pid":16072,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-d797d9b5","parentHash":"oai-2b5179a0","orgId":"org-Lk9jdNUbwX1N42B0bYwi2EPF","msg":"Cloned organization key"}
{"level":30,"time":*************,"pid":16072,"module":"server","module":"key-checker","service":"openai","parent":"oai-3b36688b","organizations":["org-fQ5wuMw3OxhV7ey3NZWgVHkr","org-upWt1FsbEIzoFcryj0sGHAPy"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":1754502212176,"pid":16072,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-8280c3e9","parentHash":"oai-3b36688b","orgId":"org-fQ5wuMw3OxhV7ey3NZWgVHkr","msg":"Cloned organization key"}
{"level":30,"time":1754502212176,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002","ft:gpt-3.5-turbo-0613:techtouch::7qabuPyG"],"msg":"Checked key."}
{"level":30,"time":1754502214465,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002"],"msg":"Checked key."}
{"level":30,"time":1754502214466,"pid":16072,"module":"server","module":"key-checker","service":"openai","callId":"yvqh0k","timeoutId":7,"msg":"Batch complete."}
{"level":20,"time":1754502214467,"pid":16072,"module":"server","module":"key-checker","service":"openai","callId":"sjckkj","timeoutId":14,"numEnabled":4,"numUnchecked":2,"msg":"Scheduling next check..."}
{"level":30,"time":1754502214467,"pid":16072,"module":"server","module":"key-checker","service":"openai","callId":"sjckkj","timeoutId":14,"batch":["oai-d797d9b5","oai-8280c3e9"],"remaining":0,"newTimeoutId":15,"msg":"Scheduled batch of initial checks."}
{"level":20,"time":1754502214736,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-d797d9b5","msg":"Checking key..."}
{"level":20,"time":1754502214738,"pid":16072,"module":"server","module":"key-checker","service":"openai","key":"oai-8280c3e9","msg":"Checking key..."}
{"level":30,"time":1754502216859,"pid":21988,"module":"server","module":"key-provider","service":"openai","keyCount":3,"msg":"Loaded OpenAI keys."}
{"level":40,"time":1754502216860,"pid":21988,"module":"server","module":"key-provider","service":"anthropic","msg":"ANTHROPIC_KEYS is not set. Anthropic API will not be available."}
{"level":40,"time":1754502216860,"pid":21988,"module":"server","module":"key-provider","service":"google-ai","msg":"GOOGLE_AI_KEYS is not set. Google AI API will not be available."}
{"level":40,"time":1754502216860,"pid":21988,"module":"server","module":"key-provider","service":"deepseek","msg":"DEEPSEEK_KEYS is not set. DeepSeek API will not be available."}
{"level":40,"time":1754502216860,"pid":21988,"module":"server","module":"key-provider","service":"xai","msg":"XAI_KEYS is not set. XAI API will not be available."}
{"level":40,"time":1754502216863,"pid":21988,"module":"server","module":"key-provider","service":"groq","msg":"GROQ_KEYS is not set. Groq API will not be available."}
{"level":30,"time":1754502216872,"pid":21988,"module":"server","module":"cidr","list":"ADMIN_WHITELIST","ranges":{"ADMIN_WHITELIST":[[{"octets":[0,0,0,0]},0],[{"parts":[0,0,0,0,0,0,0,0]},0]]},"msg":"IP whitelist configured"}
{"level":20,"time":1754502216874,"pid":21988,"module":"server","module":"check-risu-token","msg":"Importing Risu public key"}
{"level":20,"time":1754502216875,"pid":21988,"module":"server","module":"check-risu-token","msg":"Imported Risu public key"}
{"level":30,"time":1754502216918,"pid":21988,"module":"server","module":"cidr","list":"IP_BLACKLIST","ranges":{"IP_BLACKLIST":[]},"msg":"IP blacklist configured"}
{"level":30,"time":1754502216919,"pid":21988,"module":"server","msg":"Server starting up..."}
{"level":30,"time":1754502217112,"pid":21988,"module":"server","build":"c86db69 (modified) (main@yae-miko/ai-reverse-proxy)","status":"M src/shared/models.ts","changes":true,"msg":"Got build info from Git."}
{"level":30,"time":1754502217114,"pid":21988,"module":"server","msg":"Checking configs and external dependencies..."}
{"level":30,"time":1754502217115,"pid":21988,"module":"server","module":"key-checker","service":"openai","msg":"Starting key checker..."}
{"level":30,"time":1754502217117,"pid":21988,"module":"server","module":"key-checker","service":"anthropic","msg":"Starting key checker..."}
{"level":30,"time":1754502217118,"pid":21988,"module":"server","module":"key-checker","service":"google-ai","msg":"Starting key checker..."}
{"level":30,"time":1754502217118,"pid":21988,"module":"server","module":"key-checker","service":"deepseek","msg":"Starting key checker..."}
{"level":30,"time":1754502217119,"pid":21988,"module":"server","module":"key-checker","service":"xai","msg":"Starting key checker..."}
{"level":30,"time":1754502217120,"pid":21988,"module":"server","module":"key-checker","service":"groq","msg":"Starting key checker..."}
{"level":30,"time":1754502217133,"pid":21988,"module":"server","rule":"0 2,10,18 * * *","next":"2025-08-07T02:00:00.000+07:00","msg":"Scheduled periodic key recheck job"}
{"level":30,"time":1754502217561,"pid":21988,"module":"server","module":"database","msg":"Initializing database SQLite..."}
{"level":20,"time":1754502217567,"pid":21988,"module":"server","module":"database","msg":"Query: \n\t\t\tCREATE TABLE IF NOT EXISTS \"__drizzle_migrations\" (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t"}
{"level":20,"time":1754502217568,"pid":21988,"module":"server","module":"database","msg":"Query: SELECT id, hash, created_at FROM \"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1"}
{"level":20,"time":1754502217568,"pid":21988,"module":"server","module":"database","msg":"Query: BEGIN"}
{"level":20,"time":1754502217568,"pid":21988,"module":"server","module":"database","msg":"Query: COMMIT"}
{"level":30,"time":1754502217568,"pid":21988,"module":"server","module":"database","msg":"Database SQLite initialized."}
{"level":30,"time":1754502217569,"pid":21988,"module":"server","msg":"Starting request queue..."}
{"level":10,"time":1754502217569,"pid":21988,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1754502217569,"pid":21988,"module":"server","module":"request-queue","msg":"Started request queue."}
{"level":30,"time":1754502217574,"pid":21988,"module":"server","build":"c86db69 (modified) (main@yae-miko/ai-reverse-proxy)","msg":"Startup complete."}
{"level":20,"time":1754502217575,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"nh5wmy","timeoutId":7,"numEnabled":3,"numUnchecked":3,"msg":"Scheduling next check..."}
{"level":30,"time":1754502217575,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"nh5wmy","timeoutId":7,"batch":["oai-2b5179a0","oai-3b36688b","oai-ce384488"],"remaining":0,"newTimeoutId":17,"msg":"Scheduled batch of initial checks."}
{"level":40,"time":1754502217576,"pid":21988,"module":"server","module":"key-checker","service":"anthropic","callId":"mfm9gb","timeoutId":8,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502217576,"pid":21988,"module":"server","module":"key-checker","service":"google-ai","callId":"j13gq4","timeoutId":9,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502217576,"pid":21988,"module":"server","module":"key-checker","service":"deepseek","callId":"8btmy5","timeoutId":10,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502217577,"pid":21988,"module":"server","module":"key-checker","service":"xai","callId":"5lzddc","timeoutId":11,"msg":"All keys are disabled. Stopping."}
{"level":40,"time":1754502217577,"pid":21988,"module":"server","module":"key-checker","service":"groq","callId":"de1v9b","timeoutId":12,"msg":"All keys are disabled. Stopping."}
{"level":30,"time":1754502217588,"pid":21988,"module":"server","port":7860,"interface":"0.0.0.0","msg":"Server ready to accept connections."}
{"level":20,"time":1754502217830,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","msg":"Checking key..."}
{"level":20,"time":1754502217841,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","msg":"Checking key..."}
{"level":20,"time":*************,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","msg":"Checking key..."}
{"level":40,"time":*************,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-ce384488","error":{"error":{"message":"Incorrect API key provided: sk-proj-********************************************wX1c. You can find your API key at https://platform.openai.com/account/api-keys.","type":"invalid_request_error","param":null,"code":"invalid_api_key"}},"msg":"Key is invalid or revoked. Disabling key."}
{"level":40,"time":*************,"pid":21988,"module":"server","module":"key-provider","service":"openai","key":"oai-ce384488","reason":"revoked","disabledBy":"key-checker","msg":"Key disabled"}
{"level":30,"time":*************,"pid":21988,"module":"server","module":"key-checker","service":"openai","parent":"oai-3b36688b","organizations":["org-fQ5wuMw3OxhV7ey3NZWgVHkr","org-upWt1FsbEIzoFcryj0sGHAPy"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":*************,"pid":21988,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-8280c3e9","parentHash":"oai-3b36688b","orgId":"org-fQ5wuMw3OxhV7ey3NZWgVHkr","msg":"Cloned organization key"}
{"level":30,"time":*************,"pid":21988,"module":"server","module":"key-checker","service":"openai","parent":"oai-2b5179a0","organizations":["org-4zsLVMQwd6XiqMlUZrXT3mda","org-Lk9jdNUbwX1N42B0bYwi2EPF"],"msg":"Key is associated with multiple organizations; cloning key for each organization."}
{"level":30,"time":1754502219576,"pid":21988,"module":"server","module":"key-provider","service":"openai","cloneHash":"oai-d797d9b5","parentHash":"oai-2b5179a0","orgId":"org-Lk9jdNUbwX1N42B0bYwi2EPF","msg":"Cloned organization key"}
{"level":30,"time":1754502219591,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-3b36688b","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002","ft:gpt-3.5-turbo-0613:techtouch::7qabuPyG"],"msg":"Checked key."}
{"level":30,"time":1754502219627,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-2b5179a0","models":["gpt4","turbo","gpt4-turbo","gpt4o","gpt4o-mini","chatgpt-4o","o1-mini","o1","o3-mini","o3","o4-mini","gpt4.1","gpt4.1-mini","gpt4.1-nano"],"tier":5,"snapshots":["gpt-4-0613","gpt-4","gpt-3.5-turbo","o4-mini-deep-research-2025-06-26","o3-pro-2025-06-10","o4-mini-deep-research","o3-deep-research","o3-deep-research-2025-06-26","davinci-002","babbage-002","gpt-3.5-turbo-instruct","gpt-3.5-turbo-instruct-0914","dall-e-3","dall-e-2","gpt-4-1106-preview","gpt-3.5-turbo-1106","tts-1-hd","tts-1-1106","tts-1-hd-1106","text-embedding-3-small","text-embedding-3-large","gpt-4-0125-preview","gpt-4-turbo-preview","gpt-3.5-turbo-0125","gpt-4-turbo","gpt-4-turbo-2024-04-09","gpt-4o","gpt-4o-2024-05-13","gpt-4o-mini-2024-07-18","gpt-4o-mini","gpt-4o-2024-08-06","chatgpt-4o-latest","o1-mini-2024-09-12","o1-mini","gpt-4o-realtime-preview-2024-10-01","gpt-4o-audio-preview-2024-10-01","gpt-4o-audio-preview","gpt-4o-realtime-preview","omni-moderation-latest","omni-moderation-2024-09-26","gpt-4o-realtime-preview-2024-12-17","gpt-4o-audio-preview-2024-12-17","gpt-4o-mini-realtime-preview-2024-12-17","gpt-4o-mini-audio-preview-2024-12-17","o1-2024-12-17","o1","gpt-4o-mini-realtime-preview","gpt-4o-mini-audio-preview","computer-use-preview","o3-mini","o3-mini-2025-01-31","gpt-4o-2024-11-20","computer-use-preview-2025-03-11","gpt-4o-search-preview-2025-03-11","gpt-4o-search-preview","gpt-4o-mini-search-preview-2025-03-11","gpt-4o-mini-search-preview","gpt-4o-transcribe","gpt-4o-mini-transcribe","o1-pro-2025-03-19","o1-pro","gpt-4o-mini-tts","o3-2025-04-16","o4-mini-2025-04-16","o3","o4-mini","gpt-4.1-2025-04-14","gpt-4.1","gpt-4.1-mini-2025-04-14","gpt-4.1-mini","gpt-4.1-nano-2025-04-14","gpt-4.1-nano","gpt-image-1","codex-mini-latest","o3-pro","gpt-4o-realtime-preview-2025-06-03","gpt-4o-audio-preview-2025-06-03","gpt-3.5-turbo-16k","tts-1","whisper-1","text-embedding-ada-002"],"msg":"Checked key."}
{"level":30,"time":1754502219630,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"nh5wmy","timeoutId":7,"msg":"Batch complete."}
{"level":20,"time":1754502219631,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"mubmyc","timeoutId":17,"numEnabled":4,"numUnchecked":2,"msg":"Scheduling next check..."}
{"level":30,"time":1754502219631,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"mubmyc","timeoutId":17,"batch":["oai-8280c3e9","oai-d797d9b5"],"remaining":0,"newTimeoutId":50,"msg":"Scheduled batch of initial checks."}
{"level":20,"time":1754502219889,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-8280c3e9","msg":"Checking key..."}
{"level":20,"time":1754502219891,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-d797d9b5","msg":"Checking key..."}
{"level":40,"time":1754502220275,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-8280c3e9","rateLimitType":"insufficient_quota","error":{"error":{"message":"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.","type":"insufficient_quota","param":null,"code":"insufficient_quota"}},"msg":"Key returned a non-transient 429 error. Disabling key."}
{"level":40,"time":1754502220275,"pid":21988,"module":"server","module":"key-provider","service":"openai","key":"oai-8280c3e9","reason":"quota","disabledBy":"key-checker","msg":"Key disabled"}
{"level":40,"time":1754502220723,"pid":21988,"module":"server","module":"key-checker","service":"openai","key":"oai-d797d9b5","rateLimitType":"insufficient_quota","error":{"error":{"message":"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.","type":"insufficient_quota","param":null,"code":"insufficient_quota"}},"msg":"Key returned a non-transient 429 error. Disabling key."}
{"level":40,"time":1754502220724,"pid":21988,"module":"server","module":"key-provider","service":"openai","key":"oai-d797d9b5","reason":"quota","disabledBy":"key-checker","msg":"Key disabled"}
{"level":30,"time":1754502220724,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"mubmyc","timeoutId":17,"msg":"Batch complete."}
{"level":20,"time":1754502220724,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"oblzx0","timeoutId":50,"numEnabled":2,"numUnchecked":0,"msg":"Scheduling next check..."}
{"level":30,"time":1754502220724,"pid":21988,"module":"server","module":"key-checker","service":"openai","callId":"oblzx0","timeoutId":50,"msg":"Initial checks complete and recurring checks are disabled for this service. Stopping."}
{"level":10,"time":1754502237590,"pid":21988,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754502257597,"pid":21988,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1754502276863,"pid":21988,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1754502276863,"pid":21988,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1754502277598,"pid":21988,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1754502297611,"pid":21988,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}

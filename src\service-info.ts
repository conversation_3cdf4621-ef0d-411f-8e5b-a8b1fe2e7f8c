import { config, listConfig } from "./config";
import { getEstimatedWaitTime, getQueueLength } from "./proxy/queue";
import { getActiveRequests } from "./proxy/rate-limit";

import { cacheStore } from "./shared/cache";
import {
  type Anthrop<PERSON><PERSON><PERSON>,
  type DeepSeekKey,
  type <PERSON><PERSON><PERSON><PERSON><PERSON>,
  type GroqKey,
  keyPool,
  type Usages as KeyUsages,
  type OpenAIKey,
  type XAIKey,
} from "./shared/key-management";
import {
  type AnthropicModelFamily,
  type DeepSeekModelFamily,
  type GoogleAIModelFamily,
  type GroqModelFamily,
  isAllowedModel,
  LLM_SERVICES,
  type LLMService,
  MODEL_FAMILY_SERVICE,
  type ModelFamily,
  type OpenAIModelFamily,
  type XAIModelFamily,
} from "./shared/models";
import { getTokenCostUsd, getTokenFormat } from "./shared/stats";
import { getUserUsages } from "./shared/users/user-store";
import { assertNever, numberFormat, ObjectTyped } from "./shared/utils";

const CACHE_TTL = 2000;

type KeyPoolKey = ReturnType<typeof keyPool.list>[0];
const keyIsOpenAIKey = (k: KeyPoolKey): k is OpenAIKey => k.service === "openai";
const keyIsAnthropicKey = (k: KeyPoolKey): k is AnthropicKey => k.service === "anthropic";
const keyisDeepSeekKey = (k: KeyPoolKey): k is DeepSeekKey => k.service === "deepseek";
const keyIsGoogAIKey = (k: KeyPoolKey): k is GoogleAIKey => k.service === "google-ai";
const keyIsXAIKey = (k: KeyPoolKey): k is XAIKey => k.service === "xai";
const keyIsGroqKey = (k: KeyPoolKey): k is GroqKey => k.service === "groq";

/** Stats aggregated across all keys for a given service. */
type ServiceAggregate = "keys" | "uncheckedKeys" | "orgs";
/** Stats aggregated across all keys for a given model family. */
type ModelAggregates = {
  active: number;
  trial?: number;
  revoked?: number;
  overQuota?: number;
  pozzed?: number;
  queued: number;

  prompts: number;
  inputTokens: number;
  outputTokens: number;
};
/** All possible combinations of model family and aggregate type. */
type ModelAggregateKey = `${ModelFamily}__${keyof ModelAggregates}`;

type AllStats = {
  prompts: number;
  inputTokens: number;
  outputTokens: number;
  inputCost: number;
  outputCost: number;
} & { [modelFamily in ModelFamily]?: ModelAggregates } & {
  [service in LLMService as `${service}__${ServiceAggregate}`]?: number;
};

type BaseFamilyInfo = {
  inputTokens?: string;
  outputTokens?: string;
  prompts?: string;
  activeKeys: number;
  revokedKeys?: number;
  proomptersInQueue?: number;
  estimatedQueueTime?: string;
};
type OpenAIInfo = BaseFamilyInfo & {
  trialKeys?: number;
  overQuotaKeys?: number;
};
type AnthropicInfo = BaseFamilyInfo & {
  trialKeys?: number;
  prefilledKeys?: number;
  overQuotaKeys?: number;
};

// prettier-ignore
export type ServiceInfo = {
  uptime: string;
  endpoints: {
    openai?: string;
    anthropic?: string;
    "google-ai"?: string;
		"deepseek"?: string;
		"xai"?: string;
    universal?: string;
  };
  usages?: {
		inputTokens: { start: string; total?: string };
		outputTokens: { start: string; total?: string };
		tokens: { start: string; total?: string };
		prompts: { start: string; total?: string; };
		currentRequest: number;
	},
  status?: string;
  config: ReturnType<typeof listConfig>;
  build: string;
} & { [f in OpenAIModelFamily]?: OpenAIInfo }
  & { [f in AnthropicModelFamily]?: AnthropicInfo; }
  & { [f in GoogleAIModelFamily]?: BaseFamilyInfo }
	& { [f in DeepSeekModelFamily]?: BaseFamilyInfo; }
	& { [f in XAIModelFamily]?: BaseFamilyInfo; }
	& { [f in GroqModelFamily]?: BaseFamilyInfo; }
	& ReturnType<typeof getServiceModelStats>["serviceInfo"]

const SERVICE_ENDPOINTS: { [s in LLMService]: Record<string, string> } = {
  openai: { openai: `%BASE%/openai` },
  anthropic: { anthropic: `%BASE%/anthropic` },
  "google-ai": { "google-ai": `%BASE%/google-ai` },
  deepseek: { deepseek: `%BASE%/deepseek` },
  xai: { xai: `%BASE%/xai` },
  groq: { groq: `%BASE%/groq` },
};

const familyStats = new Map<ModelAggregateKey, number>();
const serviceStats = new Map<keyof AllStats, number>();

export async function buildInfo(baseUrl: string, forAdmin = false): Promise<ServiceInfo> {
  const cache = await cacheStore.get<ServiceInfo>("service-info");
  if (cache) return cache;

  const keys = keyPool.list();
  const accessibleFamilies = new Set(
    keys
      .flatMap((k) => k.modelFamilies)
      .filter(isAllowedModel)
      .toSorted((a, b) => {
        const serviceCompare = MODEL_FAMILY_SERVICE[a].localeCompare(MODEL_FAMILY_SERVICE[b]);
        return serviceCompare !== 0 ? serviceCompare : a.localeCompare(b);
      })
  );

  familyStats.clear();
  serviceStats.clear();
  keys.forEach(addKeyToAggregates);

  const endpoints = getEndpoints(baseUrl, accessibleFamilies);
  const trafficStats = await getTrafficStats();
  const { serviceInfo, modelFamilyInfo } = getServiceModelStats(accessibleFamilies);
  const status = getStatus();

  const uptime = Math.floor(process.uptime());
  const days = Math.floor(uptime / 86400);
  const hours = Math.floor((uptime % 86400) / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);
  const seconds = uptime % 60;

  const uptimeString = `${days ? days + "d " : ""}${hours ? hours + "h " : ""}${minutes ? minutes + "m " : ""}${seconds}s`;

  const cachedInfo = {
    uptime: uptimeString,
    endpoints,
    usages: trafficStats,
    ...serviceInfo,
    status,
    ...modelFamilyInfo,
    config: listConfig(),
    build: process.env.BUILD_INFO || "dev",
  };

  if (config.staticServiceInfo && !forAdmin) {
    delete cachedInfo.usages;

    for (const family of ObjectTyped.keys(modelFamilyInfo)) {
      delete cachedInfo[family]?.proomptersInQueue;
      delete cachedInfo[family]?.estimatedQueueTime;
      delete cachedInfo[family]?.inputTokens;
      delete cachedInfo[family]?.outputTokens;
      delete cachedInfo[family]?.prompts;
    }
  }

  await cacheStore.set("service-info", cachedInfo, CACHE_TTL);
  return cachedInfo;
}

function getStatus() {
  if (!config.checkKeys) return "Key checking is disabled. The data displayed are not reliable.";

  let unchecked = 0;
  for (const service of LLM_SERVICES) {
    unchecked += serviceStats.get(`${service}__uncheckedKeys`) || 0;
  }

  return unchecked ? `Checking ${unchecked} keys...` : undefined;
}

function getEndpoints(baseUrl: string, accessibleFamilies: Set<ModelFamily>) {
  const endpoints: Record<string, string> = {};
  const keys = keyPool.list();

  for (const service of LLM_SERVICES) {
    if (!keys.some((k) => k.service === service)) {
      continue;
    }

    for (const [name, url] of Object.entries(SERVICE_ENDPOINTS[service])) {
      endpoints[name] = url.replace("%BASE%", baseUrl);
    }
  }

  endpoints["universal"] = baseUrl;
  return endpoints;
}

type TrafficStats = ServiceInfo["usages"];

async function getTrafficStats(): Promise<TrafficStats> {
  const prompts = numberFormat.format(serviceStats.get("prompts") ?? 0);

  const inputTokens = serviceStats.get("inputTokens") ?? 0;
  const outputTokens = serviceStats.get("outputTokens") ?? 0;

  const inputTokenCost = serviceStats.get("inputCost") ?? 0;
  const outputTokenCost = serviceStats.get("outputCost") ?? 0;

  const usages: NonNullable<TrafficStats> = {
    currentRequest: getActiveRequests(),
    inputTokens: {
      start: getTokenFormat(inputTokens, inputTokenCost),
    },
    outputTokens: {
      start: getTokenFormat(outputTokens, outputTokenCost),
    },
    tokens: {
      start: getTokenFormat(inputTokens + outputTokens, inputTokenCost + outputTokenCost),
    },
    prompts: { start: prompts },
  };

  if (config.gatekeeper === "user_token") {
    const { totalPrompts, inputTokens, outputTokens, inputCost, outputCost } =
      await getUserUsages();

    usages.prompts.total = numberFormat.format(totalPrompts);
    usages.inputTokens.total = getTokenFormat(inputTokens, inputCost);
    usages.outputTokens.total = getTokenFormat(outputTokens, outputCost);
    usages.tokens.total = getTokenFormat(inputTokens + outputTokens, inputCost + outputCost);
  }

  return usages;
}

function getServiceModelStats(accessibleFamilies: Set<ModelFamily>) {
  const serviceInfo: {
    [s in LLMService as `${s}${"Keys" | "Orgs"}`]?: number;
  } = {};
  const modelFamilyInfo: { [f in ModelFamily]?: BaseFamilyInfo } = {};

  for (const service of LLM_SERVICES) {
    const hasKeys = serviceStats.get(`${service}__keys`) || 0;
    if (!hasKeys) continue;

    serviceInfo[`${service}Keys`] = hasKeys;
    accessibleFamilies.forEach((f) => {
      if (MODEL_FAMILY_SERVICE[f] === service) {
        modelFamilyInfo[f] = getInfoForFamily(f);
      }
    });

    if (service === "openai" && config.checkKeys) {
      serviceInfo.openaiOrgs = getUniqueOpenAIOrgs(keyPool.list());
    }
  }
  return { serviceInfo, modelFamilyInfo };
}

function getUniqueOpenAIOrgs(keys: KeyPoolKey[]) {
  const orgIds = new Set(
    keys.filter((k) => k.service === "openai").map((k: any) => k.organizationId)
  );
  return orgIds.size;
}

function increment<T extends keyof AllStats | ModelAggregateKey>(
  map: Map<T, number>,
  key: T,
  delta = 1
) {
  map.set(key, (map.get(key) || 0) + delta);
}
const addToService = increment.bind(null, serviceStats);
const addToFamily = increment.bind(null, familyStats);

function addKeyToAggregates(k: KeyPoolKey) {
  addToService("prompts", k.prompts);
  addToService("openai__keys", k.service === "openai" ? 1 : 0);
  addToService("anthropic__keys", k.service === "anthropic" ? 1 : 0);
  addToService("google-ai__keys", k.service === "google-ai" ? 1 : 0);
  addToService("deepseek__keys", k.service === "deepseek" ? 1 : 0);
  addToService("xai__keys", k.service === "xai" ? 1 : 0);
  addToService("groq__keys", k.service === "groq" ? 1 : 0);

  const sumInput = { tokens: 0, cost: 0 };
  const sumOutput = { tokens: 0, cost: 0 };

  const incrementGenericFamilyStats = (modelFamily: ModelFamily) => {
    const key = k as any;
    const { input, output, prompts } = key[`${modelFamily}-usages`] as KeyUsages;

    addToFamily(`${modelFamily}__inputTokens`, input.tokens);
    addToFamily(`${modelFamily}__outputTokens`, output.tokens);
    addToFamily(`${modelFamily}__prompts`, prompts);
    addToFamily(`${modelFamily}__revoked`, k.isRevoked ? 1 : 0);
    addToFamily(`${modelFamily}__active`, k.isDisabled ? 0 : 1);

    sumInput.tokens += input.tokens;
    sumInput.cost += input.cost;

    sumOutput.tokens += output.tokens;
    sumOutput.cost += output.cost;
  };

  switch (k.service) {
    case "openai":
      if (!keyIsOpenAIKey(k)) throw new Error("Invalid key type");
      addToService("openai__uncheckedKeys", Boolean(k.lastCheckedAt) ? 0 : 1);
      k.modelFamilies.forEach((f) => {
        incrementGenericFamilyStats(f);
        addToFamily(`${f}__overQuota`, k.isOverQuota ? 1 : 0);
      });
      break;

    case "anthropic":
      if (!keyIsAnthropicKey(k)) throw new Error("Invalid key type");
      addToService("anthropic__uncheckedKeys", Boolean(k.lastCheckedAt) ? 0 : 1);
      k.modelFamilies.forEach((f) => {
        incrementGenericFamilyStats(f);
        addToFamily(`${f}__trial`, k.tier === "free" ? 1 : 0);
        addToFamily(`${f}__overQuota`, k.isOverQuota ? 1 : 0);
        addToFamily(`${f}__pozzed`, k.isPozzed ? 1 : 0);
      });
      break;

    case "deepseek": {
      if (!keyisDeepSeekKey(k)) throw new Error("Invalid key type");
      addToService("deepseek__uncheckedKeys", Boolean(k.lastCheckedAt) ? 0 : 1);
      k.modelFamilies.forEach((f) => {
        incrementGenericFamilyStats(f);
        addToFamily(`${f}__overQuota`, k.isOverQuota ? 1 : 0);
      });
      break;
    }

    case "google-ai":
      if (!keyIsGoogAIKey(k)) throw new Error("Invalid key type");
      addToService("google-ai__uncheckedKeys", Boolean(k.lastCheckedAt) ? 0 : 1);
      k.modelFamilies.forEach((f) => {
        incrementGenericFamilyStats(f);
      });
      break;

    case "xai":
      if (!keyIsXAIKey(k)) throw new Error("Invalid key type");
      addToService("xai__uncheckedKeys", Boolean(k.lastCheckedAt) ? 0 : 1);
      k.modelFamilies.forEach((f) => {
        incrementGenericFamilyStats(f);
        addToFamily(`${f}__overQuota`, (k as XAIKey).isOverQuota ? 1 : 0);
      });
      break;

    case "groq":
      if (!keyIsGroqKey(k)) throw new Error("Invalid key type");
      addToService("groq__uncheckedKeys", Boolean(k.lastCheckedAt) ? 0 : 1);
      k.modelFamilies.forEach((f) => {
        incrementGenericFamilyStats(f);
        addToFamily(`${f}__overQuota`, (k as GroqKey).isOverQuota ? 1 : 0);
      });
      break;

    default:
      assertNever(k.service);
  }

  addToService("inputTokens", sumInput.tokens);
  addToService("inputCost", sumInput.cost);

  addToService("outputTokens", sumOutput.tokens);
  addToService("outputCost", sumOutput.cost);
}

function getInfoForFamily(family: ModelFamily): BaseFamilyInfo {
  const inputTokens = familyStats.get(`${family}__inputTokens`) ?? 0;
  const outputTokens = familyStats.get(`${family}__outputTokens`) ?? 0;

  const cost = getTokenCostUsd({ modelFamily: family, inputTokens, outputTokens });

  let info: BaseFamilyInfo & OpenAIInfo & AnthropicInfo = {
    inputTokens: getTokenFormat(inputTokens, cost.input),
    outputTokens: getTokenFormat(outputTokens, cost.output),
    prompts: numberFormat.format(familyStats.get(`${family}__prompts`) || 0),
    activeKeys: familyStats.get(`${family}__active`) || 0,
    revokedKeys: familyStats.get(`${family}__revoked`) || 0,
  };

  // Add service-specific stats to the info object.
  if (config.checkKeys) {
    const service = MODEL_FAMILY_SERVICE[family];
    switch (service) {
      case "openai":
        info.overQuotaKeys = familyStats.get(`${family}__overQuota`) || 0;
        info.trialKeys = familyStats.get(`${family}__trial`) || 0;
        break;

      case "anthropic":
        info.overQuotaKeys = familyStats.get(`${family}__overQuota`) || 0;
        info.trialKeys = familyStats.get(`${family}__trial`) || 0;
        info.prefilledKeys = familyStats.get(`${family}__pozzed`) || 0;
        break;
      case "deepseek":
        info.overQuotaKeys = familyStats.get(`${family}__overQuota`) || 0;
        break;
      case "xai":
        info.overQuotaKeys = familyStats.get(`${family}__overQuota`) || 0;
        break;
    }
  }

  // Add queue stats to the info object.
  const queue = getQueueInformation(family);
  info.proomptersInQueue = queue.proomptersInQueue;
  info.estimatedQueueTime = queue.estimatedQueueTime;

  return info;
}

/** Returns queue time in seconds, or minutes + seconds if over 60 seconds. */
function getQueueInformation(partition: ModelFamily) {
  const waitMs = getEstimatedWaitTime(partition);
  const waitTime =
    waitMs < 60000
      ? `${Math.round(waitMs / 1000)}sec`
      : `${Math.round(waitMs / 60000)}min, ${Math.round((waitMs % 60000) / 1000)}sec`;
  return {
    proomptersInQueue: getQueueLength(partition),
    estimatedQueueTime: waitMs > 2000 ? waitTime : "no wait",
  };
}

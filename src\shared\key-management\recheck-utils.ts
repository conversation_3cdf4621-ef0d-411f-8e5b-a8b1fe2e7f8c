import { logger } from "@/logger";
import type { Key } from "./index";

/**
 * Standardized validation logic for key rechecking operations.
 * Prevents reactivation of keys that should remain disabled.
 */
export type RecheckValidationOptions = {
  /** Whether to allow reactivation of revoked keys (default: false) */
  allowRevokedReactivation?: boolean;
  /** Whether to allow reactivation of manually disabled keys (default: false) */
  allowManuallyDisabledReactivation?: boolean;
  /** Custom validation function for additional checks */
  customValidation?: (key: Key) => boolean;
};

/**
 * Determines if a key should be allowed to be reactivated during a recheck operation.
 *
 * @param key The key to validate
 * @param options Validation options
 * @returns true if the key can be reactivated, false otherwise
 */
export function canReactivateKey(key: Key, options: RecheckValidationOptions = {}): boolean {
  const {
    allowRevokedReactivation = false,
    allowManuallyDisabledReactivation = false,
    customValidation,
  } = options;

  // Never reactivate revoked keys unless explicitly allowed
  if (key.isRevoked && !allowRevokedReactivation) {
    return false;
  }

  // Check if key was manually disabled (by admin or system, not key-checker)
  if (key.isDisabled && key.disabledBy && key.disabledBy !== "key-checker") {
    if (!allowManuallyDisabledReactivation) {
      return false;
    }
  }

  // Check if key was disabled due to revocation
  if (key.isDisabled && key.disabledReason === "revoked" && !allowRevokedReactivation) {
    return false;
  }

  // Apply custom validation if provided
  if (customValidation && !customValidation(key)) {
    return false;
  }

  return true;
}

/**
 * Standardized recheck update object that safely resets key properties
 * while respecting validation rules.
 *
 * @param key The key to generate updates for
 * @param options Validation options
 * @returns Partial key update object
 */
export function createStandardRecheckUpdate<T extends Key>(
  key: T,
  options: RecheckValidationOptions = {}
): Partial<T> {
  // Reset to trigger immediate recheck
  const update: Partial<T> = {};
  update.lastCheckedAt = 0;

  // Only reset these properties if the key can be reactivated
  if (canReactivateKey(key, options)) {
    update.isOverQuota = false;
    update.isDisabled = false;
  }

  return update;
}

/**
 * Logs information about keys that were not reactivated during recheck.
 *
 * @param keys Array of keys being rechecked
 * @param options Validation options used
 * @param serviceName Name of the service for logging
 */
export function logRecheckValidation(
  keys: Key[],
  options: RecheckValidationOptions,
  serviceName: string
): void {
  const log = logger.child({ module: "key-recheck", service: serviceName });
  const skippedKeys = keys.filter((key) => !canReactivateKey(key, options));

  if (skippedKeys.length > 0) {
    const reasons = skippedKeys.map((key) => ({
      hash: key.hash,
      isRevoked: key.isRevoked,
      isDisabled: key.isDisabled,
      disabledReason: key.disabledReason,
      disabledBy: key.disabledBy,
    }));

    log.info(
      {
        skippedCount: skippedKeys.length,
        totalCount: keys.length,
        reasons,
      },
      "Skipped reactivation of keys during recheck due to validation rules"
    );
  }
}

/**
 * Standardized recheck implementation that can be used by all providers.
 *
 * @param keys Array of keys to recheck
 * @param updateFn Function to update a key by hash
 * @param scheduleNextCheckFn Function to schedule the next check
 * @param serviceName Name of the service for logging
 * @param options Validation options
 */
export function performStandardRecheck<T extends Key>(
  keys: T[],
  updateFn: (hash: string, update: Partial<T>) => void,
  scheduleNextCheckFn?: () => void,
  serviceName: string = "unknown",
  options: RecheckValidationOptions = {}
): void {
  logRecheckValidation(keys, options, serviceName);

  keys.forEach((key) => {
    const update = createStandardRecheckUpdate(key, options);
    updateFn(key.hash, update);
  });

  // Schedule next check if function provided
  scheduleNextCheckFn?.();
}

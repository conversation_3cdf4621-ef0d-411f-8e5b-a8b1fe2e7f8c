import {
  MODEL_FAMILIES,
  MODEL_FAMILY_SERVICE,
  getModelFamilyForService,
  type LLMService,
  type ModelFamily,
} from "../models";
import { defaultUsages, type Usages } from "./index";

/**
 * Build the property name used to store per-family usages.
 * Example: "gpt-4" -> "gpt-4-usages"
 */
export function toUsageKey(family: ModelFamily): `${ModelFamily}-usages` {
  return `${family}-usages` as const;
}

/**
 * Given a model string and service, resolve its ModelFamily and return the usages key.
 */
export function getModelFamilyUsagesKey(
  model: string,
  service: LLMService
): `${ModelFamily}-usages` {
  const family = getModelFamilyForService(model, service);
  return toUsageKey(family);
}

/**
 * Return all usages keys supported by a specific service, derived from MODEL_FAMILIES.
 * This auto-updates when src/shared/models.ts adds/removes families.
 */
export function getAllModelFamilyUsageKeysForService(
  service: LLMService
): `${ModelFamily}-usages`[] {
  return MODEL_FAMILIES.filter((f) => MODEL_FAMILY_SERVICE[f] === service).map(toUsageKey);
}

/**
 * Build a usages record for all model families supported by a service.
 * Returns a new object with structuredClone(defaultUsages) for each entry to avoid shared references.
 */
export function buildServiceUsagesRecord(
  service: LLMService
): Record<`${ModelFamily}-usages`, Usages> {
  const entries = getAllModelFamilyUsageKeysForService(service).map(
    (k) => [k, structuredClone(defaultUsages)] as const
  );
  return Object.fromEntries(entries) as Record<`${ModelFamily}-usages`, Usages>;
}
